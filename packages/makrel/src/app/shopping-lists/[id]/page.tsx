import { get } from '@/lib/api/torsk-api-server'
import type { ShoppingListDetailType } from '@mercanto/api-types'
import { ErrorPage } from '@/app/_components/ErrorPage'
import DetailTable from '@/app/_components/DetailsTable'
import PageHeading from '@/app/_components/PageHeading'
import ShoppingListItemsTable from './_components/ShoppingListItemsTable'

type PageProps = {
  params: Promise<{ id: string }>
  searchParams: Promise<Record<string, string>>
}

export default async function ShoppingListDetailPage({
  params,
  searchParams
}: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  const { id } = resolvedParams
  const backUrl = resolvedSearchParams.backUrl
    ? decodeURIComponent(resolvedSearchParams.backUrl)
    : '/shopping-lists'

  let shoppingListDetail = null
  let error = null

  try {
    const response = await get<ShoppingListDetailType>(
      `/v1/shopping-lists/${id}`
    )

    if (response.error) {
      error = response.error
    } else {
      shoppingListDetail = response.data
    }
  } catch (err) {
    error =
      err instanceof Error
        ? err
        : new Error('Failed to fetch shopping list details')
  }

  if (error) {
    return (
      <ErrorPage error={error} title="Error Loading Shopping List Detail" />
    )
  }

  if (!shoppingListDetail) {
    return <ErrorPage title="Shopping List Not Found" />
  }

  const shoppingListInfoParams = [
    {
      label: 'Merchant',
      value: shoppingListDetail.merchant
    },
    {
      label: 'Shopping List Key',
      value: shoppingListDetail.merchantShoppingListKey
    },
    {
      label: 'Shopping List Name',
      value: shoppingListDetail.shoppingListName
    },
    {
      label: 'Shopping List Description',
      value: shoppingListDetail.shoppingListDescription || '-'
    },
    {
      label: 'Customer Number',
      value: shoppingListDetail.merchantCustomerKey
    }
  ]

  return (
    <main>
      <PageHeading backLink={{ title: 'Shopping Lists', href: backUrl }}>
        Details for the shopping list: {shoppingListDetail.id}
      </PageHeading>

      <div className="mb-6">
        <DetailTable params={shoppingListInfoParams} />
      </div>

      <div className="mb-6">
        <h2 className="mb-2 text-lg font-semibold">List items</h2>
        {shoppingListDetail.items.length > 0 ? (
          <ShoppingListItemsTable items={shoppingListDetail.items} />
        ) : (
          <p className="text-sm italic">
            No items found in this shopping list.
          </p>
        )}
      </div>
    </main>
  )
}
