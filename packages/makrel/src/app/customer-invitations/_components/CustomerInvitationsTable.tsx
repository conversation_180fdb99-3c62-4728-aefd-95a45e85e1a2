'use client'

import React from 'react'
import type { CustomerInvitationResponseType } from '@mercanto/api-types'
import { formatDateTimeDisplay } from '@/utils/date'
import AsyncAppTable from '@/app/_components/AsyncAppTable'
import { DEFAULT_PAGE_SIZE_OPTIONS } from '@/app/_components/AppPagination'
import LoaderOverlay from '@/app/_components/LoaderOverlay'
import { Link, Tooltip } from '@heroui/react'
import { Eye } from 'lucide-react'
import { usePathname, useSearchParams } from 'next/navigation'

type CustomerInvitationsTableProps = {
  customerInvitations: CustomerInvitationResponseType[]
  currentPage?: number
  totalPages?: number
  pageSize?: number
  isLoading?: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

export default function CustomerInvitationsTable({
  customerInvitations,
  currentPage = 1,
  totalPages = 1,
  pageSize = 25,
  isLoading = false,
  onPageChange,
  onPageSizeChange
}: CustomerInvitationsTableProps) {
  const searchParams = useSearchParams()
  const pathname = usePathname()

  const columns = [
    { key: 'merchant', label: 'Merchant' },
    { key: 'merchantCustomerKey', label: 'Merchant Customer Key' },
    { key: 'companyName', label: 'Company Name' },
    { key: 'lastUpdatedAt', label: 'Last Import' },
    { key: 'hasProblems', label: 'Problems?' },
    { key: 'deleted', label: 'Deleted?' },
    { key: 'matchingStatusCompany', label: 'Matching Status' },
    { key: 'exportedAt', label: 'Exported' }
  ]

  const renderMatchingStatus = (item: CustomerInvitationResponseType) => {
    const companyStatus = (() => {
      switch (item.matchingStatusCompany) {
        case 'unresolved':
          return '⚠️'
        case 'ignored':
          return '⏸️ (ignored)'
        case 'new':
        case 'matched':
        case 'exported':
          return `✅ (${item.matchingStatusCompany})`
        default:
          return item.matchingStatusCompany
      }
    })()

    const renderStatusForCounts = (
      total: number,
      resolved: number,
      ignored: number
    ) => {
      const current = total - ignored

      const parts: string[] = []

      if (current > 0) {
        const icon = current > resolved ? '⚠️' : '✅'
        parts.push(`${icon} (${resolved} of ${current})`)
      }

      if (ignored > 0) {
        parts.push(`⏸️ (${ignored})`)
      }

      return parts.join(', ')
    }

    const customersStatus = renderStatusForCounts(
      item.numCustomers ?? 0,
      item.numResolvedCustomers ?? 0,
      item.numIgnoredCustomers ?? 0
    )

    const businessUnitsStatus = renderStatusForCounts(
      item.numBusinessUnits ?? 0,
      item.numResolvedBusinessUnits ?? 0,
      item.numIgnoredBusinessUnits ?? 0
    )

    const rows = [
      { label: 'Company', value: companyStatus },
      { label: 'Customers', value: customersStatus || '-' },
      { label: 'Business units', value: businessUnitsStatus || '-' }
    ]

    return (
      <dl className="grid grid-cols-2 text-xs">
        {rows.map(({ label, value }) => (
          <React.Fragment key={label}>
            <dt className="font-normal m-1">{label}:</dt>
            <dd className="m-1">{value}</dd>
          </React.Fragment>
        ))}
      </dl>
    )
  }

  const renderExportedStatus = (item: CustomerInvitationResponseType) => {
    const companyStatus = item.exportedAt
      ? `✅ (${formatDateTimeDisplay(item.exportedAt)})`
      : '⚠️'

    const renderExportedForCounts = (
      total: number,
      exported: number,
      ignored: number
    ) => {
      const current = total - ignored

      if (current <= 0) {
        return ''
      }

      const icon = current > exported ? '⚠️' : '✅'
      return `${icon} (${exported} of ${current})`
    }

    const customersStatus = renderExportedForCounts(
      item.numCustomers || 0,
      item.numExportedCustomers || 0,
      item.numIgnoredCustomers || 0
    )

    const businessUnitsStatus = renderExportedForCounts(
      item.numBusinessUnits || 0,
      item.numExportedBusinessUnits || 0,
      item.numIgnoredBusinessUnits || 0
    )

    const rows = [
      { label: 'Company', value: companyStatus },
      { label: 'Customers', value: customersStatus || '-' },
      { label: 'Business units', value: businessUnitsStatus || '-' },
    ]

    return (
      <dl className="grid grid-cols-2 text-xs">
        {rows.map(({ label, value }) => (
          <React.Fragment key={label}>
            <dt className="font-normal m-1">{label}:</dt>
            <dd className="m-1">{value}</dd>
          </React.Fragment>
        ))}
      </dl>
    )
  }

  const renderCell = (
    item: CustomerInvitationResponseType,
    columnKey: React.Key
  ) => {
    const currentUrl = searchParams.toString()
      ? `${pathname}?${searchParams.toString()}`
      : pathname
    const detailPath = `/customer-invitations/${item.merchant}/${item.merchantCustomerKey}?backUrl=${encodeURIComponent(currentUrl)}`

    if (columnKey === 'actions') {
      return (
        <div className="flex space-x-2">
          <Tooltip content="View Customer Invitation" offset={0}>
            <Link href={detailPath}>
              <Eye size={20} />
            </Link>
          </Tooltip>
        </div>
      )
    }

    if (columnKey === 'merchantCustomerKey') {
      return item.merchantCustomerKey
    }

    if (columnKey === 'companyName') {
      return item.document?.company_name || '-'
    }

    if (columnKey === 'lastUpdatedAt') {
      return formatDateTimeDisplay(item.lastUpdatedAt)
    }

    if (columnKey === 'hasProblems') {
      return item.hasProblems ? 'Yes' : 'No'
    }

    if (columnKey === 'deleted') {
      return item.deleted ? 'Yes' : 'No'
    }

    if (columnKey === 'matchingStatusCompany') {
      return renderMatchingStatus(item)
    }

    if (columnKey === 'exportedAt') {
      return renderExportedStatus(item)
    }

    const value = item[columnKey as keyof CustomerInvitationResponseType]
    if (!value) return '-'

    return value.toString()
  }

  return (
    <div className="relative">
      {isLoading && <LoaderOverlay message="Loading data..." />}
      <AsyncAppTable
        columns={columns}
        items={customerInvitations}
        identifierPropertyName="id"
        renderCell={renderCell}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        pageSizeOptions={DEFAULT_PAGE_SIZE_OPTIONS}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  )
}
